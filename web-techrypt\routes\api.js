/**
 * API routes for Techrypt application
 */

const express = require('express');
const mongoose = require('mongoose');
const router = express.Router();

// Get all users from MongoDB users collection
router.get('/users', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    // Fetch all users from the collection
    const users = await usersCollection.find({}).toArray();
    
    // Remove _id field from each user document
    const usersList = users.map(user => {
      const { _id, ...userWithoutId } = user;
      return userWithoutId;
    });

    res.json({
      success: true,
      count: usersList.length,
      users: usersList
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fetch users from database'
    });
  }
});

// Get all appointments from MongoDB appointments collection
router.get('/appointments', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const appointmentsCollection = db.collection('appointments');
    
    // Fetch all appointments from the collection
    const appointments = await appointmentsCollection.find({}).toArray();
    
    // Remove _id field from each appointment document
    const appointmentsList = appointments.map(appointment => {
      const { _id, ...appointmentWithoutId } = appointment;
      return appointmentWithoutId;
    });

    res.json({
      success: true,
      count: appointmentsList.length,
      appointments: appointmentsList
    });

  } catch (error) {
    console.error('Error fetching appointments:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fetch appointments from database'
    });
  }
});

// Create a new appointment
router.post('/appointments', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const appointmentsCollection = db.collection('appointments');
    
    // Get appointment data from request body
    const appointmentData = {
      ...req.body,
      created_at: new Date(),
      updated_at: new Date()
    };

    // Insert the new appointment
    const result = await appointmentsCollection.insertOne(appointmentData);

    res.status(201).json({
      success: true,
      message: 'Appointment created successfully',
      appointmentId: result.insertedId,
      appointment: appointmentData
    });

  } catch (error) {
    console.error('Error creating appointment:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to create appointment'
    });
  }
});

// Health check endpoint
router.get('/health', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    
    // Test database connection by getting collection info
    const usersCollection = db.collection('users');
    const appointmentsCollection = db.collection('appointments');
    
    const usersCount = await usersCollection.countDocuments({});
    const appointmentsCount = await appointmentsCollection.countDocuments({});

    res.json({
      status: 'healthy',
      database: 'connected',
      collections: {
        users: usersCount,
        appointments: appointmentsCount
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Health check failed:', error);
    res.status(500).json({
      status: 'unhealthy',
      database: 'disconnected',
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Get user by ID
router.get('/users/:id', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const usersCollection = db.collection('users');
    
    const userId = req.params.id;
    const user = await usersCollection.findOne({ user_id: userId });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    // Remove _id field
    const { _id, ...userWithoutId } = user;

    res.json({
      success: true,
      user: userWithoutId
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fetch user from database'
    });
  }
});

// Get appointment by ID
router.get('/appointments/:id', async (req, res) => {
  try {
    const db = mongoose.connection.db;
    const appointmentsCollection = db.collection('appointments');
    
    const appointmentId = req.params.id;
    const appointment = await appointmentsCollection.findOne({ appointment_id: appointmentId });

    if (!appointment) {
      return res.status(404).json({
        success: false,
        message: 'Appointment not found'
      });
    }

    // Remove _id field
    const { _id, ...appointmentWithoutId } = appointment;

    res.json({
      success: true,
      appointment: appointmentWithoutId
    });

  } catch (error) {
    console.error('Error fetching appointment:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fetch appointment from database'
    });
  }
});

module.exports = router;
