<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🤖 Techrypt Smart Chatbot - Production Ready</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 0;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            width: 90%;
            max-width: 1000px;
            height: 90vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px 30px;
            text-align: center;
            border-radius: 20px 20px 0 0;
        }

        .header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        .chat-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #28a745;
            border-radius: 3px;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 8px;
        }
        .user-message {
            background: #007bff;
            color: white;
            text-align: right;
        }
        .bot-message {
            background: #28a745;
            color: white;
        }
        .input-container {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        input[type="text"] {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .error {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
            color: #721c24;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .test-btn {
            padding: 8px 15px;
            background: #28a745;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
        }
        .test-btn:hover {
            background: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Techrypt Smart Chatbot Test</h1>
        <div id="status" class="status">
            ✅ Backend Server: Connected<br>
            🤖 AI Model: microsoft/DialoGPT-medium<br>
            📊 Training Data: 10,000+ examples<br>
            🧠 Business Intelligence: Active
        </div>

        <div class="test-buttons">
            <button class="test-btn" onclick="testMessage('hello')">Test Hello</button>
            <button class="test-btn" onclick="testMessage('I have a restaurant')">Test Restaurant</button>
            <button class="test-btn" onclick="testMessage('I need a website')">Test Website</button>
            <button class="test-btn" onclick="testMessage('What services do you offer?')">Test Services</button>
            <button class="test-btn" onclick="testMessage('I want to schedule a meeting')">Test Appointment</button>
            <button class="test-btn" onclick="clearChat()">Clear Chat</button>
        </div>

        <div id="chatContainer" class="chat-container">
            <div class="message bot-message">
                🤖 Hello! I'm the Techrypt Smart Chatbot. I'm ready to help you with our digital services. Try sending me a message!
            </div>
        </div>

        <div class="input-container">
            <input type="text" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
            <button onclick="sendMessage()">Send</button>
        </div>

        <div id="responseTime" style="text-align: center; color: #666; margin-top: 10px;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5000';
        
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            // Add user message to chat
            addMessage(message, 'user');
            input.value = '';
            
            // Show typing indicator
            const typingDiv = addMessage('🤖 Typing...', 'bot');
            
            try {
                const startTime = Date.now();
                
                const response = await fetch(`${API_BASE}/chat`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        user_name: 'Test User',
                        user_context: {}
                    })
                });
                
                const data = await response.json();
                const responseTime = Date.now() - startTime;
                
                // Remove typing indicator
                typingDiv.remove();
                
                if (response.ok) {
                    // Add bot response
                    addMessage(data.response, 'bot');
                    
                    // Show response time
                    document.getElementById('responseTime').textContent = 
                        `⚡ Response time: ${responseTime}ms | Model: ${data.model || 'Unknown'}`;
                    
                    // Update status
                    updateStatus('success', `✅ Response received in ${responseTime}ms`);
                } else {
                    addMessage(`❌ Error: ${data.error || 'Unknown error'}`, 'bot');
                    updateStatus('error', `❌ Error: ${data.error || 'Unknown error'}`);
                }
                
            } catch (error) {
                // Remove typing indicator
                typingDiv.remove();
                addMessage(`❌ Connection Error: ${error.message}`, 'bot');
                updateStatus('error', `❌ Connection Error: ${error.message}`);
            }
        }
        
        function addMessage(text, type) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = text;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
            return messageDiv;
        }
        
        function updateStatus(type, message) {
            const statusDiv = document.getElementById('status');
            statusDiv.className = type === 'error' ? 'status error' : 'status';
            statusDiv.innerHTML = message;
        }
        
        function testMessage(message) {
            document.getElementById('messageInput').value = message;
            sendMessage();
        }
        
        function clearChat() {
            const chatContainer = document.getElementById('chatContainer');
            chatContainer.innerHTML = '<div class="message bot-message">🤖 Chat cleared! Ready for new conversation.</div>';
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        // Test backend connection on load
        window.onload = async function() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    updateStatus('success', `
                        ✅ Backend Server: Connected<br>
                        🤖 AI Model: ${data.llm_model}<br>
                        📊 Requests Served: ${data.requests_served}<br>
                        🧠 Status: ${data.status}
                    `);
                } else {
                    updateStatus('error', '❌ Backend connection failed');
                }
            } catch (error) {
                updateStatus('error', `❌ Backend connection error: ${error.message}`);
            }
        };
    </script>
</body>
</html>
