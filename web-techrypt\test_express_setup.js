/**
 * Test script to verify Express.js setup and database connection
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:5000';

async function testEndpoint(url, method = 'GET', data = null) {
  try {
    console.log(`\n🧪 Testing ${method} ${url}`);
    
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      timeout: 5000
    };
    
    if (data) {
      config.data = data;
      config.headers = { 'Content-Type': 'application/json' };
    }
    
    const response = await axios(config);
    console.log(`✅ Status: ${response.status}`);
    console.log(`📊 Response:`, JSON.stringify(response.data, null, 2));
    return true;
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
    if (error.response) {
      console.log(`📊 Error Response:`, JSON.stringify(error.response.data, null, 2));
    }
    return false;
  }
}

async function runTests() {
  console.log('🚀 Starting Express.js API Tests');
  console.log('=====================================');
  
  const tests = [
    // Basic endpoints
    { url: '/', description: 'Root endpoint' },
    { url: '/health', description: 'Health check' },
    
    // API endpoints
    { url: '/api/health', description: 'API health check' },
    { url: '/api/users', description: 'Get all users' },
    { url: '/api/appointments', description: 'Get all appointments' },
    
    // Test creating an appointment
    {
      url: '/api/appointments',
      method: 'POST',
      data: {
        user_name: 'Test User',
        email: '<EMAIL>',
        phone: '+**********',
        service: 'Web Development',
        date: '2024-01-15',
        time: '10:00 AM',
        message: 'Test appointment from Express.js setup'
      },
      description: 'Create test appointment'
    }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    const success = await testEndpoint(
      test.url, 
      test.method || 'GET', 
      test.data || null
    );
    
    if (success) {
      passed++;
    } else {
      failed++;
    }
    
    // Wait a bit between tests
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  console.log('\n📊 Test Results');
  console.log('================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  
  if (failed === 0) {
    console.log('\n🎉 All tests passed! Your Express.js backend is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the server logs and ensure:');
    console.log('   - Express.js server is running on port 5000');
    console.log('   - MongoDB Atlas connection is working');
    console.log('   - All dependencies are installed');
  }
}

// Check if server is running first
async function checkServerStatus() {
  try {
    console.log('🔍 Checking if Express.js server is running...');
    await axios.get(`${BASE_URL}/health`, { timeout: 3000 });
    console.log('✅ Server is running!');
    return true;
  } catch (error) {
    console.log('❌ Server is not responding.');
    console.log('💡 Please start the server first:');
    console.log('   - Run: node server.js');
    console.log('   - Or: npm start');
    console.log('   - Or: start_express_server.bat');
    return false;
  }
}

// Main execution
async function main() {
  const serverRunning = await checkServerStatus();
  
  if (serverRunning) {
    await runTests();
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Run the tests
main().catch(console.error);
