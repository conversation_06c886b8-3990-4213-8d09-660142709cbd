/**
 * <PERSON>ript to check existing database collections and their data
 */

const mongoose = require('mongoose');
require('dotenv').config();

async function checkDatabaseCollections() {
  try {
    console.log('🔍 Checking Database Collections');
    console.log('================================\n');

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      dbName: process.env.MONGODB_DATABASE
    });

    console.log('✅ Connected to MongoDB Atlas');
    console.log(`📊 Database: ${process.env.MONGODB_DATABASE}\n`);

    const db = mongoose.connection.db;

    // Get all collections in the database
    const collections = await db.listCollections().toArray();
    console.log('📁 Available Collections:');
    collections.forEach(collection => {
      console.log(`   - ${collection.name}`);
    });
    console.log('');

    // Check each expected collection
    const expectedCollections = ['users', 'appointments', 'conversations'];
    
    for (const collectionName of expectedCollections) {
      console.log(`🔍 Checking ${collectionName} collection:`);
      
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments({});
        console.log(`   📊 Total documents: ${count}`);
        
        if (count > 0) {
          // Get a sample document to show structure
          const sampleDoc = await collection.findOne({});
          if (sampleDoc) {
            console.log('   📄 Sample document structure:');
            const { _id, ...docWithoutId } = sampleDoc;
            const fields = Object.keys(docWithoutId);
            fields.forEach(field => {
              const value = docWithoutId[field];
              const type = typeof value;
              const preview = type === 'string' && value.length > 50 
                ? value.substring(0, 50) + '...' 
                : value;
              console.log(`      ${field}: ${type} - ${preview}`);
            });
          }
        } else {
          console.log('   ⚠️  Collection is empty');
        }
        
        console.log('');
      } catch (error) {
        console.log(`   ❌ Error accessing ${collectionName}: ${error.message}\n`);
      }
    }

    // Test API endpoints
    console.log('🧪 Testing API Endpoints:');
    console.log('========================\n');
    
    const testEndpoints = [
      { name: 'Users', collection: 'users' },
      { name: 'Appointments', collection: 'appointments' },
      { name: 'Conversations', collection: 'conversations' }
    ];

    for (const endpoint of testEndpoints) {
      try {
        const collection = db.collection(endpoint.collection);
        const data = await collection.find({}).limit(5).toArray();
        
        console.log(`📊 ${endpoint.name} (first 5 records):`);
        if (data.length > 0) {
          data.forEach((item, index) => {
            const { _id, ...itemWithoutId } = item;
            console.log(`   ${index + 1}. ${JSON.stringify(itemWithoutId, null, 6).substring(0, 100)}...`);
          });
        } else {
          console.log('   No data found');
        }
        console.log('');
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}\n`);
      }
    }

    // Summary
    console.log('📋 Summary:');
    console.log('===========');
    
    for (const collectionName of expectedCollections) {
      try {
        const collection = db.collection(collectionName);
        const count = await collection.countDocuments({});
        const status = count > 0 ? '✅' : '⚠️ ';
        console.log(`${status} ${collectionName}: ${count} documents`);
      } catch (error) {
        console.log(`❌ ${collectionName}: Error - ${error.message}`);
      }
    }

    console.log('\n🎯 API Endpoints Available:');
    console.log('GET  /api/users - Get all users');
    console.log('GET  /api/appointments - Get all appointments');
    console.log('GET  /api/conversations - Get all conversations');
    console.log('GET  /api/health - Database health check');
    console.log('\n💡 Test these endpoints at: http://localhost:5000/api/');

  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔌 Database connection closed');
  }
}

// Run the check
checkDatabaseCollections().catch(console.error);
