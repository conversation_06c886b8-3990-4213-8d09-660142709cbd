{"name": "techrypt-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mongodb", "techrypt", "backend"], "author": "", "license": "ISC", "description": "Express.js backend for Techrypt application", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "json2csv": "^5.0.7", "mongoose": "^8.0.0", "morgan": "^1.10.0", "node-schedule": "^2.1.1", "nodemailer": "^6.10.1"}, "devDependencies": {"nodemon": "^3.0.1"}}