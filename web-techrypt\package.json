{"name": "techrypt-backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "mongodb", "techrypt", "backend"], "author": "", "license": "ISC", "description": "Express.js backend for Techrypt application", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.0", "dotenv": "^16.3.1", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.1"}}