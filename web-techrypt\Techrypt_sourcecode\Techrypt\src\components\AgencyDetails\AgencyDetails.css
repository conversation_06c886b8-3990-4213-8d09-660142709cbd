.zorka-agency {
  display: flex
;
  justify-content: center;
  align-items: center;
  height: 100%;
  /* margin-top: -50px; */
  /* z-index: 10; */
  position: relative;
  gap: 40px;
  color: var(--color-white);
  padding-top: 88px;
  padding-bottom: 16px;
  /* padding: 16px 0px; */
}


.zorka-agency h1 {
  font-size: 5rem;
  /* margin-bottom: 180px; */
}

.img-agency {
  /* margin-bottom: 155px; */
  width: 400px;
}
.info-item span {
  font-size: 40px;
  border-bottom: 2px solid var(--color-white);
  border-top: 2px solid var(--color-white);
  display: flex;
  flex-direction: column;
  padding: 10px;
}

.info-item span:nth-child(4) {
  border: none;
}
.info-item span:nth-child(2) {
  border: none;
}

@media only screen and (max-width: 600px) {
  .zorka-agency {
    flex-direction: column;
  }
  .zorka-agency h1 {
    font-size: 3rem;
    margin-bottom: 0px;
  }
  .img-agency {
    margin-bottom: 0px;
    width: 300px;
  }

  .info-item span {
    font-size: 22px;
  }
}
