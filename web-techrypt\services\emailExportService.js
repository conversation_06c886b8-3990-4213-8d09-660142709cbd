/**
 * Email Export Service for Express.js Backend
 * Handles weekly and manual email exports using MongoDB Atlas
 */

const nodemailer = require('nodemailer');
const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { Parser } = require('json2csv');
require('dotenv').config();

class EmailExportService {
  constructor() {
    this.adminEmail = '<EMAIL>';
    this.exportDir = 'weekly_exports';
    this.setupEmailTransporter();
    this.ensureExportDirectory();
  }

  setupEmailTransporter() {
    // Use custom SMTP configuration from .env
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_SERVER || 'smtp.techrypt.io',
      port: parseInt(process.env.SMTP_PORT) || 587,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USERNAME || process.env.SENDER_EMAIL,
        pass: process.env.SMTP_PASSWORD || process.env.EMAIL_PASSWORD
      },
      tls: {
        rejectUnauthorized: false // For development - use proper certs in production
      }
    });

    console.log('✅ Email transporter configured');
  }

  ensureExportDirectory() {
    if (!fs.existsSync(this.exportDir)) {
      fs.mkdirSync(this.exportDir, { recursive: true });
      console.log(`✅ Created export directory: ${this.exportDir}`);
    }
  }

  async exportCollectionToCSV(collectionName, timestamp) {
    try {
      const db = mongoose.connection.db;
      const collection = db.collection(collectionName);
      
      // Fetch all documents
      const documents = await collection.find({}).toArray();
      
      if (documents.length === 0) {
        console.log(`⚠️  No data found in ${collectionName} collection`);
        return null;
      }

      // Remove _id field and convert to plain objects
      const cleanedData = documents.map(doc => {
        const { _id, ...cleanDoc } = doc;
        return cleanDoc;
      });

      // Convert to CSV
      const parser = new Parser();
      const csv = parser.parse(cleanedData);
      
      // Save to file
      const filename = `${collectionName}_${timestamp}.csv`;
      const filepath = path.join(this.exportDir, filename);
      fs.writeFileSync(filepath, csv);
      
      console.log(`✅ Exported ${documents.length} records from ${collectionName} to ${filename}`);
      return {
        filename,
        filepath,
        count: documents.length,
        size: fs.statSync(filepath).size
      };

    } catch (error) {
      console.error(`❌ Error exporting ${collectionName}:`, error.message);
      return null;
    }
  }

  async generateExports(timestamp) {
    console.log('📊 Starting data export...');
    
    const collections = ['users', 'appointments', 'conversations'];
    const exportedFiles = {};

    for (const collection of collections) {
      const result = await this.exportCollectionToCSV(collection, timestamp);
      if (result) {
        exportedFiles[collection] = result;
      }
    }

    return exportedFiles;
  }

  async getDatabaseStats() {
    try {
      const db = mongoose.connection.db;
      
      const stats = {
        total_users: await db.collection('users').countDocuments({}),
        total_appointments: await db.collection('appointments').countDocuments({}),
        total_conversations: await db.collection('conversations').countDocuments({}),
        pending_appointments: await db.collection('appointments').countDocuments({ status: 'pending' }),
        completed_appointments: await db.collection('appointments').countDocuments({ status: 'completed' }),
        last_updated: new Date().toISOString()
      };

      return stats;
    } catch (error) {
      console.error('❌ Error getting database stats:', error.message);
      return {};
    }
  }

  createSummaryReport(timestamp, stats, exportedFiles) {
    const report = `
📊 TECHRYPT WEEKLY DATA EXPORT REPORT
Generated: ${new Date().toLocaleString()}
Export Timestamp: ${timestamp}

📈 DATABASE STATISTICS:
• Total Users: ${stats.total_users || 0}
• Total Appointments: ${stats.total_appointments || 0}
• Total Conversations: ${stats.total_conversations || 0}
• Pending Appointments: ${stats.pending_appointments || 0}
• Completed Appointments: ${stats.completed_appointments || 0}

📁 EXPORTED FILES:
${Object.entries(exportedFiles).map(([collection, file]) => 
  `• ${collection}: ${file.filename} (${file.count} records, ${(file.size / 1024).toFixed(1)} KB)`
).join('\n')}

🔍 DATA INSIGHTS:
• Export includes all historical data
• Files are ready for analysis and backup
• Data exported from MongoDB Atlas

📞 CONTACT INFORMATION:
For questions about this export, contact the development team.

---
Automated Export System (Express.js)
Techrypt Database Management
    `.trim();

    return report;
  }

  async sendEmail(recipientEmail, exportedFiles, timestamp, isManual = false) {
    try {
      console.log(`📧 Sending email to ${recipientEmail}...`);

      // Get database statistics
      const stats = await this.getDatabaseStats();
      
      // Create summary report
      const summaryReport = this.createSummaryReport(timestamp, stats, exportedFiles);

      // Email subject
      const subject = isManual 
        ? `Techrypt Manual Data Export - ${timestamp}`
        : `Techrypt Weekly Data Export - ${timestamp}`;

      // Email body
      const emailBody = `
Dear ${isManual ? 'Recipient' : 'Admin'},

Please find attached the ${isManual ? 'manual' : 'weekly'} data export from the Techrypt database.

${summaryReport}

📁 ATTACHED FILES:
${Object.values(exportedFiles).map(file => 
  `• ${file.filename} (${(file.size / 1024).toFixed(1)} KB)`
).join('\n')}

This export was generated ${isManual ? 'manually' : 'automatically'} on ${new Date().toLocaleString()}.

Best regards,
Techrypt Database Management System (Express.js)
      `.trim();

      // Prepare attachments
      const attachments = Object.values(exportedFiles).map(file => ({
        filename: file.filename,
        path: file.filepath
      }));

      // Send email
      const mailOptions = {
        from: process.env.SENDER_EMAIL || process.env.SMTP_USERNAME,
        to: recipientEmail,
        subject: subject,
        text: emailBody,
        attachments: attachments
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`✅ Email sent successfully to ${recipientEmail}`);
      console.log(`📧 Message ID: ${result.messageId}`);
      
      return true;

    } catch (error) {
      console.error(`❌ Email sending error:`, error.message);
      return false;
    }
  }

  async performWeeklyExport() {
    console.log('🚀 Starting weekly export process...');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                     new Date().toTimeString().split(' ')[0].replace(/:/g, '');

    try {
      // Generate exports
      const exportedFiles = await this.generateExports(timestamp);

      if (Object.keys(exportedFiles).length === 0) {
        console.log('❌ No files were exported');
        return false;
      }

      // Send email to admin
      const emailSent = await this.sendEmail(this.adminEmail, exportedFiles, timestamp, false);

      if (emailSent) {
        console.log('🎉 Weekly export completed successfully!');
        this.cleanupOldFiles();
        return true;
      } else {
        console.log('❌ Email sending failed');
        return false;
      }

    } catch (error) {
      console.error('❌ Weekly export failed:', error.message);
      return false;
    }
  }

  async performManualExport(recipientEmail) {
    console.log(`🚀 Starting manual export for ${recipientEmail}...`);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0] + '_' + 
                     new Date().toTimeString().split(' ')[0].replace(/:/g, '') + '_manual';

    try {
      // Generate exports
      const exportedFiles = await this.generateExports(timestamp);

      if (Object.keys(exportedFiles).length === 0) {
        console.log('❌ No files were exported');
        return false;
      }

      // Send email
      const emailSent = await this.sendEmail(recipientEmail, exportedFiles, timestamp, true);

      if (emailSent) {
        console.log(`🎉 Manual export sent successfully to ${recipientEmail}!`);
        return true;
      } else {
        console.log('❌ Email sending failed');
        return false;
      }

    } catch (error) {
      console.error('❌ Manual export failed:', error.message);
      return false;
    }
  }

  cleanupOldFiles(keepWeeks = 4) {
    console.log(`🧹 Cleaning up files older than ${keepWeeks} weeks...`);
    
    try {
      const cutoffTime = Date.now() - (keepWeeks * 7 * 24 * 60 * 60 * 1000);
      const files = fs.readdirSync(this.exportDir);

      let deletedCount = 0;
      files.forEach(file => {
        const filePath = path.join(this.exportDir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          fs.unlinkSync(filePath);
          console.log(`🗑️  Deleted old file: ${file}`);
          deletedCount++;
        }
      });

      console.log(`✅ Cleanup completed: ${deletedCount} files deleted`);

    } catch (error) {
      console.error('❌ Cleanup error:', error.message);
    }
  }

  async testEmailConfiguration() {
    console.log('🧪 Testing email configuration...');
    
    try {
      await this.transporter.verify();
      console.log('✅ Email configuration test successful');
      return true;
    } catch (error) {
      console.error('❌ Email configuration test failed:', error.message);
      return false;
    }
  }
}

module.exports = EmailExportService;
