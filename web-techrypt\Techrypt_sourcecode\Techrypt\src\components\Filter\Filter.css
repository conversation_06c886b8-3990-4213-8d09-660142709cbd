.filter-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #0f0f0f;
  color: var(--color-white);
  overflow: hidden;
  height: auto;
  padding-bottom: 40px;
}
.filter-section-filter-container {
  width: 85vw;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.filter-h1 {
  font-size: 5rem;
  padding: 40px;
  text-align: center;
}

select {
  width: 220px;
  height: 35px;
  border-radius: 25px;
  border: 2px solid #ccc;
  background-color: transparent;
  color: #ccc;
  padding: 0 5px;
  margin-left: 5px;
  margin-bottom: 10px;
}
option {
  background-color: black;
}
.filter-rows {
  width: 100%;
  height: auto;
}
.filter-rows:hover {
  color: #444;
  border-color: #444;
}

.show-more-container {
  text-align: center;
  margin-top: 20px;
}

.show-more-button {
  background-color: var(--color-yellow); /* Blue background */
  color: var(--color-white); /* White text */
  padding: 10px 20px; /* Padding for the button */
  border: none; /* Remove default border */
  border-radius: 50px; /* Rounded corners */
  font-size: 16px; /* Font size */
  cursor: pointer; /* Pointer cursor on hover */
  transition: background-color 0.3s ease; /* Smooth hover transition */
}

.show-more-button:focus {
  outline: none; /* Remove focus outline */
}

.show-more-button:active {
  background-color: var(--color-yellow); /* Even darker blue on click */
  transform: scale(0.98); /* Slightly reduce size on click */
}

@media (max-width: 768px) {
  .filter-h1 {
    font-size: 3rem;
    text-align: center;
  }
  .filter-select {
    text-align: center;
  }
}
