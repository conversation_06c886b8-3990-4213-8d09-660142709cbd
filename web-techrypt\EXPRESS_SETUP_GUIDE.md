# Express.js Backend Setup Guide for Techrypt

This guide will help you set up and run the Express.js backend for your Techrypt application.

## Prerequisites

### 1. Install Node.js
- Download and install Node.js from [https://nodejs.org/](https://nodejs.org/)
- Choose the LTS (Long Term Support) version
- This will also install npm (Node Package Manager)

### 2. Verify Installation
Open Command Prompt or PowerShell and run:
```bash
node --version
npm --version
```

## Setup Instructions

### 1. Navigate to Project Directory
```bash
cd web-techrypt
```

### 2. Install Dependencies
```bash
npm install
```

This will install the following packages:
- **express**: Web framework for Node.js
- **mongoose**: MongoDB object modeling for Node.js
- **dotenv**: Environment variable loader
- **cors**: Cross-Origin Resource Sharing middleware
- **helmet**: Security middleware
- **morgan**: HTTP request logger
- **express-rate-limit**: Rate limiting middleware
- **nodemon**: Development dependency for auto-restarting server

### 3. Environment Configuration
Your `.env` file is already configured with:
- MongoDB Atlas connection string
- Database name: `techrypt_chatbot`
- Email configuration

### 4. Start the Server

#### Option A: Using Batch File (Windows)
```bash
start_express_server.bat
```

#### Option B: Using PowerShell Script
```bash
.\start_express_server.ps1
```

#### Option C: Direct Node.js Command
```bash
node server.js
```

#### Option D: Development Mode (with auto-restart)
```bash
npm run dev
```

## API Endpoints

Once the server is running on `http://localhost:5000`, you can access:

### Base Endpoints
- `GET /` - Welcome message and API information
- `GET /health` - Server health check

### API Endpoints (prefix: `/api`)
- `GET /api/users` - Get all users from MongoDB
- `GET /api/users/:id` - Get specific user by ID
- `GET /api/appointments` - Get all appointments from MongoDB
- `GET /api/appointments/:id` - Get specific appointment by ID
- `POST /api/appointments` - Create new appointment
- `GET /api/health` - Database health check

### Example API Calls

#### Get All Users
```bash
curl http://localhost:5000/api/users
```

#### Get All Appointments
```bash
curl http://localhost:5000/api/appointments
```

#### Create New Appointment
```bash
curl -X POST http://localhost:5000/api/appointments \
  -H "Content-Type: application/json" \
  -d '{
    "user_name": "John Doe",
    "email": "<EMAIL>",
    "phone": "+**********",
    "service": "Web Development",
    "date": "2024-01-15",
    "time": "10:00 AM"
  }'
```

## Project Structure

```
web-techrypt/
├── server.js              # Main Express.js server file
├── config/
│   └── database.js         # MongoDB connection configuration
├── routes/
│   └── api.js             # API route definitions
├── package.json           # Node.js dependencies and scripts
├── .env                   # Environment variables
├── start_express_server.bat    # Windows batch startup script
├── start_express_server.ps1    # PowerShell startup script
└── EXPRESS_SETUP_GUIDE.md      # This guide
```

## Features

### Security
- **Helmet**: Sets various HTTP headers for security
- **CORS**: Configured for frontend origins (localhost:3000, localhost:5173)
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: JSON body parsing with size limits

### Database
- **MongoDB Atlas**: Cloud database connection
- **Mongoose**: ODM for MongoDB with connection pooling
- **Error Handling**: Comprehensive error handling for database operations

### Development
- **Morgan**: HTTP request logging
- **Nodemon**: Auto-restart during development
- **Environment Variables**: Secure configuration management

## Troubleshooting

### Common Issues

1. **Node.js not found**
   - Ensure Node.js is installed and added to PATH
   - Restart terminal after installation

2. **MongoDB connection failed**
   - Check `.env` file for correct MongoDB URI
   - Verify MongoDB Atlas cluster is running
   - Check network connectivity

3. **Port 5000 already in use**
   - Change PORT in `.env` file or kill existing process
   - Use `netstat -ano | findstr :5000` to find process using port

4. **Dependencies installation failed**
   - Clear npm cache: `npm cache clean --force`
   - Delete `node_modules` and `package-lock.json`, then run `npm install`

### Development Tips

1. **Use development mode** for auto-restart:
   ```bash
   npm run dev
   ```

2. **Check logs** for debugging information

3. **Test API endpoints** using tools like Postman or curl

4. **Monitor database** using MongoDB Compass

## Next Steps

1. **Frontend Integration**: Update your React frontend to use these API endpoints
2. **Authentication**: Add user authentication if needed
3. **Validation**: Add input validation for API endpoints
4. **Testing**: Write unit and integration tests
5. **Deployment**: Deploy to cloud platforms like Heroku, Vercel, or AWS

## Support

If you encounter any issues:
1. Check the console logs for error messages
2. Verify all dependencies are installed
3. Ensure MongoDB Atlas connection is working
4. Check that all environment variables are set correctly
