/**
 * Simple test to check API endpoints and database collections
 * Run this while your Express.js server is running
 */

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:5000';

function makeRequest(path) {
  return new Promise((resolve, reject) => {
    const url = `${BASE_URL}${path}`;
    
    http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (error) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    }).on('error', (error) => {
      reject(error);
    });
  });
}

async function testEndpoints() {
  console.log('🧪 Testing Techrypt Express.js API');
  console.log('==================================\n');

  const endpoints = [
    { path: '/', name: 'Welcome Page' },
    { path: '/health', name: 'Server Health' },
    { path: '/api/health', name: 'Database Health' },
    { path: '/api/users', name: 'Users Collection' },
    { path: '/api/appointments', name: 'Appointments Collection' },
    { path: '/api/conversations', name: 'Conversations Collection' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`🔍 Testing ${endpoint.name} (${endpoint.path})`);
      const result = await makeRequest(endpoint.path);
      
      if (result.status === 200) {
        console.log(`✅ Status: ${result.status}`);
        
        if (endpoint.path.includes('/api/') && endpoint.path !== '/api/health') {
          // For collection endpoints, show count
          if (result.data.count !== undefined) {
            console.log(`📊 Count: ${result.data.count} documents`);
          }
          if (result.data.users) {
            console.log(`👥 Users: ${result.data.users.length} found`);
          }
          if (result.data.appointments) {
            console.log(`📅 Appointments: ${result.data.appointments.length} found`);
          }
          if (result.data.conversations) {
            console.log(`💬 Conversations: ${result.data.conversations.length} found`);
          }
        } else if (endpoint.path === '/api/health') {
          // For health endpoint, show collection counts
          if (result.data.collections) {
            console.log(`📊 Collections:`);
            Object.entries(result.data.collections).forEach(([name, count]) => {
              console.log(`   ${name}: ${count} documents`);
            });
          }
        } else {
          // For other endpoints, show basic info
          if (result.data.message) {
            console.log(`📝 Message: ${result.data.message}`);
          }
        }
      } else {
        console.log(`⚠️  Status: ${result.status}`);
        console.log(`📝 Response: ${JSON.stringify(result.data, null, 2)}`);
      }
      
      console.log('');
      
      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 500));
      
    } catch (error) {
      console.log(`❌ Error: ${error.message}\n`);
    }
  }

  console.log('🎯 Test Complete!');
  console.log('================');
  console.log('If you see data counts above, your database collections are working correctly.');
  console.log('If counts are 0, the collections exist but are empty.');
  console.log('If you see errors, there might be connection issues.');
  console.log('\n💡 You can also test these URLs in your browser:');
  endpoints.forEach(endpoint => {
    console.log(`   ${BASE_URL}${endpoint.path}`);
  });
}

// Check if server is running first
async function checkServer() {
  try {
    await makeRequest('/health');
    console.log('✅ Server is running on http://localhost:5000\n');
    return true;
  } catch (error) {
    console.log('❌ Server is not running or not accessible');
    console.log('💡 Please start the server first with: node server.js\n');
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (serverRunning) {
    await testEndpoints();
  }
}

main().catch(console.error);
