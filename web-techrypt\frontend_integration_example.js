/**
 * Frontend Integration Example for React
 * This shows how to connect your React frontend to the Express.js backend
 */

// API Configuration
const API_BASE_URL = 'http://localhost:5000/api';

// API Service Class
class TechryptAPI {
  constructor(baseURL = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  // Helper method for making API calls
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error(`API Error (${endpoint}):`, error);
      throw error;
    }
  }

  // Get all users
  async getUsers() {
    return this.makeRequest('/users');
  }

  // Get user by ID
  async getUserById(userId) {
    return this.makeRequest(`/users/${userId}`);
  }

  // Get all appointments
  async getAppointments() {
    return this.makeRequest('/appointments');
  }

  // Get appointment by ID
  async getAppointmentById(appointmentId) {
    return this.makeRequest(`/appointments/${appointmentId}`);
  }

  // Create new appointment
  async createAppointment(appointmentData) {
    return this.makeRequest('/appointments', {
      method: 'POST',
      body: JSON.stringify(appointmentData)
    });
  }

  // Health check
  async healthCheck() {
    return this.makeRequest('/health');
  }
}

// React Hook Example
// You can use this in your React components

/*
import { useState, useEffect } from 'react';

// Custom hook for API calls
export const useAPI = () => {
  const [api] = useState(() => new TechryptAPI());
  return api;
};

// Custom hook for fetching users
export const useUsers = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const api = useAPI();

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const response = await api.getUsers();
        setUsers(response.users || []);
        setError(null);
      } catch (err) {
        setError(err.message);
        setUsers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [api]);

  return { users, loading, error };
};

// Custom hook for fetching appointments
export const useAppointments = () => {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const api = useAPI();

  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        setLoading(true);
        const response = await api.getAppointments();
        setAppointments(response.appointments || []);
        setError(null);
      } catch (err) {
        setError(err.message);
        setAppointments([]);
      } finally {
        setLoading(false);
      }
    };

    fetchAppointments();
  }, [api]);

  const createAppointment = async (appointmentData) => {
    try {
      const response = await api.createAppointment(appointmentData);
      setAppointments(prev => [...prev, response.appointment]);
      return response;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return { appointments, loading, error, createAppointment };
};

// Example React Component
export const AppointmentsList = () => {
  const { appointments, loading, error } = useAppointments();

  if (loading) return <div>Loading appointments...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Appointments ({appointments.length})</h2>
      {appointments.map((appointment, index) => (
        <div key={index} className="appointment-card">
          <h3>{appointment.user_name}</h3>
          <p>Email: {appointment.email}</p>
          <p>Phone: {appointment.phone}</p>
          <p>Service: {appointment.service}</p>
          <p>Date: {appointment.date}</p>
          <p>Time: {appointment.time}</p>
        </div>
      ))}
    </div>
  );
};

// Example Appointment Form Component
export const AppointmentForm = () => {
  const { createAppointment } = useAppointments();
  const [formData, setFormData] = useState({
    user_name: '',
    email: '',
    phone: '',
    service: '',
    date: '',
    time: '',
    message: ''
  });
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      await createAppointment(formData);
      alert('Appointment created successfully!');
      setFormData({
        user_name: '',
        email: '',
        phone: '',
        service: '',
        date: '',
        time: '',
        message: ''
      });
    } catch (error) {
      alert('Failed to create appointment: ' + error.message);
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <form onSubmit={handleSubmit}>
      <input
        type="text"
        name="user_name"
        placeholder="Full Name"
        value={formData.user_name}
        onChange={handleChange}
        required
      />
      <input
        type="email"
        name="email"
        placeholder="Email"
        value={formData.email}
        onChange={handleChange}
        required
      />
      <input
        type="tel"
        name="phone"
        placeholder="Phone"
        value={formData.phone}
        onChange={handleChange}
        required
      />
      <input
        type="text"
        name="service"
        placeholder="Service"
        value={formData.service}
        onChange={handleChange}
        required
      />
      <input
        type="date"
        name="date"
        value={formData.date}
        onChange={handleChange}
        required
      />
      <input
        type="time"
        name="time"
        value={formData.time}
        onChange={handleChange}
        required
      />
      <textarea
        name="message"
        placeholder="Additional message"
        value={formData.message}
        onChange={handleChange}
      />
      <button type="submit" disabled={submitting}>
        {submitting ? 'Creating...' : 'Create Appointment'}
      </button>
    </form>
  );
};
*/

// Export the API class for use in your React app
export default TechryptAPI;

// Usage example:
/*
// In your React component:
import TechryptAPI from './path/to/this/file';

const api = new TechryptAPI();

// Get users
const users = await api.getUsers();

// Create appointment
const newAppointment = await api.createAppointment({
  user_name: 'John Doe',
  email: '<EMAIL>',
  phone: '+1234567890',
  service: 'Web Development',
  date: '2024-01-15',
  time: '10:00 AM'
});
*/
