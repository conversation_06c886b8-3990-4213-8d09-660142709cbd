.folded-corner-card {
    position: relative;
    /* background: white; */
    /* padding: calc(var(--fold-size) * 1.5); */
    /* margin: 1em; */
    /* box-shadow: 0 2px 5px rgba(0,0,0,0.1); */
  }
  
  .folded-corner-card::before {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    border-width: 0 var(--fold-size) var(--fold-size) 0;
    border-style: solid;
    background: #000;
  }
  
  .folded-corner-card::after {
    content: "";
    position: absolute;
    top: 0;
    right: 0;
    border-width: 0 var(--fold-size) var(--fold-size) 0;
    border-style: solid;
    border-color: #E2C27B   #121212;
    transition: all 0.3s ease;
  }
  
  .folded-corner-card:hover::after {
    border-width: 0 calc(var(--fold-size) * 1.2) calc(var(--fold-size) * 1.2) 0;
  }